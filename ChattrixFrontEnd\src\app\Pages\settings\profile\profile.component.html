<!-- Profile Page Container -->
<div class="profile-container" *ngIf="userProfile$ | async as userProfile">
  <!-- Profile Header Section -->
  <div class="profile-header">
    <mat-card class="header-card">
      <div class="header-content">
        <!-- Profile Picture and Basic Info -->
        <div class="profile-info-section">
          <div
            class="profile-avatar"
            [class.has-image]="userProfile.profilePictureUrl"
          >
            <img
              *ngIf="userProfile.profilePictureUrl"
              [src]="userProfile.profilePictureUrl"
              [alt]="getUserDisplayName(userProfile)"
              class="avatar-image"
              #avatarImg
              (error)="avatarImg.style.display = 'none'"
            />
            <span
              *ngIf="!userProfile.profilePictureUrl"
              class="avatar-initials"
            >
              {{ getUserInitials(userProfile) }}
            </span>
          </div>

          <div class="profile-details">
            <h1 class="profile-name">{{ getUserDisplayName(userProfile) }}</h1>
            <p class="profile-role">{{ getRoleDisplay(userProfile) }}</p>
            <p class="profile-email">{{ userProfile.email }}</p>
          </div>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Tab Navigation -->
  <div class="tab-navigation">
    <mat-tab-group
      [selectedIndex]="selectedTabIndex"
      (selectedTabChange)="onTabChange($event.index)"
      class="profile-tabs"
    >
      <!-- Profile Info Tab -->
      <mat-tab label="Personal Info">
        <div class="tab-content">
          <mat-card class="form-card">
            <mat-card-header class="form-card-header">
              <mat-card-title>Personal Information</mat-card-title>
              <mat-card-subtitle
                >Update your personal details</mat-card-subtitle
              >
            </mat-card-header>

            <mat-card-content>
              <!-- Profile Picture Upload Section -->
              <div class="profile-picture-section">
                <h3 class="section-title">Profile Picture</h3>
                <div class="profile-picture-container">
                  <div
                    class="profile-picture-preview clickable"
                    [class.has-image]="
                      imagePreview || userProfile.profilePictureUrl
                    "
                    (click)="triggerFileInput($event)"
                    (keydown.enter)="triggerFileInput($event)"
                    (keydown.space)="triggerFileInput($event)"
                    tabindex="0"
                    role="button"
                    [attr.aria-label]="
                      imagePreview || userProfile.profilePictureUrl
                        ? 'Change profile picture'
                        : 'Upload profile picture'
                    "
                  >
                    <img
                      *ngIf="imagePreview"
                      [src]="imagePreview"
                      alt="Profile Preview"
                      class="preview-image"
                    />
                    <img
                      *ngIf="!imagePreview && userProfile.profilePictureUrl"
                      [src]="userProfile.profilePictureUrl"
                      alt="Current Profile Picture"
                      class="preview-image"
                    />
                    <div
                      *ngIf="!imagePreview && !userProfile.profilePictureUrl"
                      class="placeholder-content"
                    >
                      <mat-icon class="upload-icon">add_a_photo</mat-icon>
                      <span class="upload-text">Add Photo</span>
                    </div>
                    <div
                      *ngIf="imagePreview || userProfile.profilePictureUrl"
                      class="overlay-content"
                    >
                      <mat-icon class="change-icon">edit</mat-icon>
                      <span class="change-text">Change</span>
                    </div>
                    <button
                      *ngIf="imagePreview || userProfile.profilePictureUrl"
                      type="button"
                      mat-icon-button
                      class="remove-image-btn"
                      (click)="removeImage($event)"
                      aria-label="Remove image"
                    >
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>

                  <input
                    type="file"
                    id="profileFileInput"
                    accept="image/jpeg,image/jpg,image/png"
                    (change)="onFileSelected($event)"
                    style="display: none"
                  />

                  <p class="upload-hint">
                    Supported formats: JPEG, JPG, PNG (Max 5MB)
                  </p>
                </div>
              </div>

              <form
                [formGroup]="profileForm"
                (ngSubmit)="onSubmitProfile()"
                class="profile-form"
              >
                <!-- Full Name Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>First Name*</mat-label>
                  <input
                    matInput
                    formControlName="fullName"
                    placeholder="Enter your full name"
                    autocomplete="name"
                  />
                  <mat-error *ngIf="getFieldError('fullName')">
                    {{ getFieldError("fullName") }}
                  </mat-error>
                </mat-form-field>

                <!-- Email Field (Read-only) -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Email</mat-label>
                  <input
                    matInput
                    [value]="userProfile.email"
                    disabled
                    class="readonly-field"
                  />
                  <mat-icon matSuffix>info</mat-icon>
                  <mat-hint [style]="{ color: 'var(--text-muted)' }"
                    >Email cannot be changed from profile settings</mat-hint
                  >
                </mat-form-field>

                <!-- Phone Number Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Phone Number</mat-label>
                  <input
                    matInput
                    formControlName="phoneNumber"
                    placeholder="+****************"
                    autocomplete="tel"
                  />
                  <mat-icon matSuffix>phone</mat-icon>
                </mat-form-field>

                <!-- User Role Field (Read-only) -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>User type</mat-label>
                  <input
                    matInput
                    [value]="getRoleDisplay(userProfile)"
                    disabled
                    class="readonly-field"
                  />
                  <mat-icon matSuffix>info</mat-icon>
                  <mat-hint [style]="{ color: 'var(--text-muted)' }"
                    >User role is managed by administrators</mat-hint
                  >
                </mat-form-field>

                <!-- Description Field -->
                <mat-form-field
                  appearance="outline"
                  class="form-field description-field"
                >
                  <mat-label>Email Signature</mat-label>
                  <textarea
                    matInput
                    formControlName="description"
                    placeholder="Add a brief description about yourself"
                    rows="4"
                    maxlength="500"
                  ></textarea>
                  <mat-hint align="end">
                    {{ profileForm.get("description")?.value?.length || 0 }}/500
                  </mat-hint>
                </mat-form-field>

                <!-- Action Buttons -->
                <div class="form-actions">
                  <button
                    type="button"
                    mat-stroked-button
                    class="cancel-button"
                    (click)="onCancelProfile()"
                    [disabled]="isSubmitting"
                  >
                    Cancel
                  </button>

                  <button
                    type="submit"
                    mat-raised-button
                    class="submit-button"
                    [disabled]="profileForm.invalid || isSubmitting"
                  >
                    <mat-spinner
                      *ngIf="isSubmitting"
                      diameter="20"
                      class="button-spinner"
                    ></mat-spinner>
                    <span [class.hidden]="isSubmitting">Update Profile</span>
                  </button>
                </div>
              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Change Password Tab -->
      <mat-tab label="Change Password">
        <div class="tab-content">
          <mat-card class="form-card">
            <mat-card-header>
              <mat-card-title>Change Password</mat-card-title>
              <mat-card-subtitle
                >Update your account password</mat-card-subtitle
              >
            </mat-card-header>

            <mat-card-content>
              <div class="password-redirect">
                <p>Click the button below to change your password securely.</p>
                <button
                  mat-raised-button
                  class="submit-button"
                  (click)="onTabChange(1)"
                >
                  Change Password
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="!(userProfile$ | async)" class="loading-container">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading profile...</p>
</div>
